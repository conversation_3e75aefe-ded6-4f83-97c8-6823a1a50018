{%- if section.blocks.size > 0 -%}
  <section class="product-cards-section">
    <div class="product-cards-container">
      {%- for block in section.blocks -%}
        {%- if block.type == 'product_card' -%}
          <div class="product-card" {{ block.shopify_attributes }}>
            <div class="product-card-image" style="background-color: {{ block.settings.placeholder_bg_color | default: '#F5F5F5' }};">
              {%- if block.settings.image -%}
                <img
                  src="{{ block.settings.image | image_url: width: 500 }}"
                  srcset="{{ block.settings.image | image_url: width: 250 }} 250w,
                          {{ block.settings.image | image_url: width: 500 }} 500w,
                          {{ block.settings.image | image_url: width: 750 }} 750w"
                  sizes="(max-width: 768px) 100vw, 50vw"
                  alt="{{ block.settings.image.alt | default: block.settings.title | escape }}"
                  loading="lazy"
                  width="500"
                  height="250"
                >
              {%- endif -%}
            </div>
            
            <div class="product-card-content">
              {%- if block.settings.title != blank -%}
                <h3 class="product-card-title">{{ block.settings.title }}</h3>
              {%- endif -%}
              
              {%- if block.settings.description != blank -%}
                <p class="product-card-description">{{ block.settings.description }}</p>
              {%- endif -%}
              
              {%- if block.settings.button_text != blank and block.settings.button_url != blank -%}
                <a href="{{ block.settings.button_url }}" class="product-card-button">
                  {{ block.settings.button_text }}
                </a>
              {%- endif -%}
            </div>
          </div>
        {%- endif -%}
      {%- endfor -%}
    </div>
  </section>

  <style>
    .product-cards-section {
      padding: 40px 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .product-cards-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 73px;
      justify-items: center;
    }

    .product-card {
      border-radius: 31.718px;
      background: #FFF;
      box-shadow: 0 1.057px 1.057px 0 rgba(0, 0, 0, 0.15);
      padding: 20px;
      text-align: center;
      max-width: 529px;
      max-height: 485px;
      width: 100%;
    }

    .product-card-image {
      width: calc(100% + 40px);
      height: 295px;
      margin: -20px -20px 15px -20px;
      overflow: hidden;
      border-radius: 31.718px 31.718px 0 0;
      position: relative;
    }

    .product-card-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
      border-radius: 31.718px 31.718px 0 0;
    }

    .product-card-content {
      padding: 0 10px;
    }

    .product-card-title {
      color: #4a4a4a;
      text-align: center;
      font-family: Lato, sans-serif;
      font-size: 21.145px;
      font-style: normal;
      font-weight: 700;
      line-height: 25.374px; /* 120% */
      letter-spacing: 0.148px;
      margin: 0 auto 4px auto;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }

    .product-card-description {
      color: #808080;
      text-align: center;
      font-family: Lato, sans-serif;
      font-size: 15.859px;
      font-style: normal;
      font-weight: 400;
      line-height: 25.374px; /* 160% */
      letter-spacing: 0.148px;
      margin: 0 auto 25px auto;
      max-width: 260px;
    }

    .product-card-button {
      display: inline-block;
      width: 295px;
      height: 50px;
      padding: 0;
      border-radius: 43.348px;
      background: #00AEF8;
      box-shadow: 0 4px 16px 0 rgba(0, 158, 224, 0.40);
      color: #FFF;
      text-align: center;
      font-family: Lato, sans-serif;
      font-size: 16.916px;
      font-style: normal;
      font-weight: 900;
      line-height: 54px;
      text-transform: uppercase;
      text-decoration: none;
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
      border: none;
      cursor: pointer;
    }

    .product-card-button:hover {
      background: #33BFFF;
      box-shadow: 0 6px 20px 0 rgba(0, 158, 224, 0.60);
      color: #FFF;
      text-decoration: none;
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
      .product-cards-section {
        padding: 30px 15px;
      }

      .product-cards-container {
        grid-template-columns: 1fr;
        gap: 30px;
      }

      .product-card {
        max-width: 100%;
        max-height: none;
      }

      .product-card-title {
        width: auto;
        font-size: 19px;
      }

      .product-card-description {
        font-size: 14px;
      }

      .product-card-button {
        width: min(295px, calc(100vw - 80px));
        height: 54px;
        font-size: 15px;
        line-height: 54px;
        padding: 0;
      }

      .product-card-image {
        height: 240px;
        width: calc(100% + 30px);
        margin: -15px -15px 15px -15px;
      }
    }

    @media (max-width: 480px) {
      .product-cards-section {
        padding: 20px 10px;
      }

      .product-cards-container {
        gap: 25px;
      }

      .product-card {
        padding: 15px;
      }

      .product-card-image {
        height: 200px;
        width: calc(100% + 30px);
        margin: -15px -15px 15px -15px;
      }

      .product-card-button {
        width: min(295px, calc(100vw - 60px));
        height: 50px;
        font-size: 14px;
        line-height: 50px;
      }
    }
  </style>
{%- endif -%}

{% schema %}
{
  "name": "Product Cards",
  "class": "shopify-section-product-cards",
  "settings": [
    {
      "type": "paragraph",
      "content": "Create customizable product cards with images, titles, descriptions, and buttons."
    }
  ],
  "blocks": [
    {
      "type": "product_card",
      "name": "Product Card",
      "limit": 2,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Card Image"
        },
        {
          "type": "color",
          "id": "placeholder_bg_color",
          "label": "Background Color (when no image)",
          "default": "#F5F5F5"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Card Title",
          "default": "On My Feet All Day"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Card Description",
          "default": "100% custom-made. Ideal if you're 4+ hours a day on your feet."
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button Text",
          "default": "SHOP NOW"
        },
        {
          "type": "url",
          "id": "button_url",
          "label": "Button Link"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Product Cards",
      "blocks": [
        {
          "type": "product_card",
          "settings": {
            "title": "On My Feet All Day",
            "description": "100% custom-made. Ideal if you're 4+ hours a day on your feet.",
            "button_text": "SHOP NOW"
          }
        },
        {
          "type": "product_card",
          "settings": {
            "title": "Normal Everyday Activity",
            "description": "100% custom-made. Great for day-to day comfort.",
            "button_text": "SHOP NOW"
          }
        }
      ]
    }
  ]
}
{% endschema %}
